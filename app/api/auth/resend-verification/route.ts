import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";

import { validateData } from "@/lib/validation";
import { rateLimitConfigs } from "@/lib/rateLimit";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { AuthService } from "@/lib/services/auth";
import { withApiErrorHandling } from "@/lib/error-handler";

// Resend verification email validation
const resendVerificationSchema = z.object({
  email: z.email("Invalid email format"),
});

async function resendVerificationHandler(
  request: NextRequest,
): Promise<NextResponse> {
  const body = await request.json();

  // Validate input using Zod schema
  const validation = validateData(resendVerificationSchema, body);

  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  const { email } = validation.data;

  // Use AuthService to resend verification email
  await AuthService.resendVerificationEmail(email);

  return NextResponse.json(
    {
      message:
        "If an account with this email exists and is not verified, a verification email has been sent.",
    },
    { status: 200 },
  );
}

// Apply middleware with error handling
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(withApiErrorHandling(resendVerificationHandler));
