import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { rateLimitConfigs } from "@/lib/rateLimit";
import { UserService } from "@/lib/services/user";
import { ApiErrorHandler } from "@/lib/error-handler";

// Validation schema
const updateProfileSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name too long")
    .optional(),
});

const updateProfileHandler = ApiErrorHandler.withErrorHandling(
  async (request: NextRequest) => {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 },
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = updateProfileSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Invalid input",
          details: validationResult.error.message,
        },
        { status: 400 },
      );
    }

    const { name } = validationResult.data;

    // Update user profile using UserService.updateUser - now returns data directly
    const updatedUser = await UserService.updateUser(session.user.id, {
      name,
    });

    return NextResponse.json(
      {
        message: "Profile updated successfully",
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          name: updatedUser.name,
          roles: updatedUser.roles,
          emailVerified: updatedUser.emailVerified,
          createdAt: updatedUser.createdAt,
        },
      },
      { status: 200 },
    );
  },
);

// Apply middleware
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(updateProfileHandler);
