import { NextRequest, NextResponse } from "next/server";
import { Role } from "@prisma/client";
import { z } from "zod/v4";

import { validateData } from "@/lib/validation";
import { rateLimitConfigs } from "@/lib/rateLimit";
import {
  withMiddleware,
  withSecurityHeaders,
  withRateLimit,
} from "@/lib/middleware";
import { AuthService } from "@/lib/services/auth";
import { withApiErrorHandling } from "@/lib/error-handler";

// User registration validation
const registerSchema = z.object({
  email: z.email("Invalid email format"),
  password: z.string().min(8, "Password must be at least 8 characters long"),
  roles: z
    .array(z.enum(["MODEL_PROVIDER", "ADVERTISER"]))
    .optional()
    .default([]), // Roles are optional during registration, default to empty array
});

async function registerHandler(request: NextRequest): Promise<NextResponse> {
  const body = await request.json();

  // Validate input using Zod schema
  const validation = validateData(registerSchema, body);

  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  const { email, password, roles } = validation.data;

  // Use AuthService to register user
  const user = await AuthService.registerUser({
    email,
    password,
    roles: roles as Role[],
  });

  return NextResponse.json(
    {
      message:
        "User registered successfully. Please check your email for verification.",
      user,
      emailSent: true,
    },
    { status: 201 },
  );
}

// Apply middleware with error handling
export const POST = withMiddleware(
  withSecurityHeaders(),
  withRateLimit(rateLimitConfigs.auth),
)(withApiErrorHandling(registerHandler));
