import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";

import { validateData } from "@/lib/validation";
import { AdService } from "@/lib/services/ad";
import { withApiErrorHandling } from "@/lib/error-handler";

// Serve ad request validation
const serveAdSchema = z.object({
  appId: z.string().min(1, "App ID is required"),
  appSecret: z.string().min(1, "App secret is required"),
  topics: z.array(z.string()).optional(),
  userContext: z
    .object({
      userAgent: z.string().optional(),
      language: z.string().optional(),
    })
    .optional(),
});

async function serveAdHandler(request: NextRequest): Promise<NextResponse> {
  const body = await request.json();

  // Validate input using Zod schema
  const validation = validateData(serveAdSchema, body);

  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  const { appId, appSecret, topics, userContext } = validation.data;

  // Use AdService to serve ad
  const result = await AdService.serveAd({
    appId,
    appSecret,
    topics,
    userContext,
  });

  return NextResponse.json(result);
}

// Apply error handling middleware
export const POST = withApiErrorHandling(serveAdHandler);

// GET endpoint for testing/documentation
export async function GET(_request: NextRequest) {
  return NextResponse.json({
    message: "AI Ad Platform - Ad Serving API",
    version: "1.0.0",
    endpoints: {
      "POST /api/serve-ad": {
        description: "Request an advertisement for your AI application",
        required: ["appId", "appSecret"],
        optional: ["topics", "userContext"],
        example: {
          appId: "app_xxxxxxxxxxxxxxxx",
          appSecret: "secret_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
          topics: ["AI", "productivity", "automation"],
          userContext: {
            userAgent: "Mozilla/5.0...",
            language: "en-US",
          },
        },
      },
      "POST /api/impressions": {
        description: "Track ad impressions and clicks",
        required: ["adId", "appId"],
        optional: ["clicked", "userAgent"],
        example: {
          adId: "ad_xxxxxxxxxxxxxxxx",
          appId: "app_xxxxxxxxxxxxxxxx",
          clicked: false,
          userAgent: "Mozilla/5.0...",
        },
      },
    },
  });
}
