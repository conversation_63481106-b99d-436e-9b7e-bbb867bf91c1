import { NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";

import { validateData } from "@/lib/validation";
import { AdService } from "@/lib/services/ad";
import { withApiErrorHandling } from "@/lib/error-handler";

// Enhanced impression tracking validation
const impressionTrackingSchema = z.object({
  adId: z.string().min(1, "Ad ID is required"),
  appId: z.string().min(1, "App ID is required"),
  clicked: z.boolean().default(false),
  userAgent: z.string().optional(),
});

async function recordImpressionHandler(
  request: NextRequest,
): Promise<NextResponse> {
  const body = await request.json();

  // Validate input using Zod schema
  const validation = validateData(impressionTrackingSchema, body);

  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  const { adId, appId, clicked, userAgent } = validation.data;

  // Get client IP address
  const forwarded = request.headers.get("x-forwarded-for");
  const ipAddress = forwarded
    ? forwarded.split(",")[0]
    : request.headers.get("x-real-ip") || "unknown";

  // Use AdService to record impression
  await AdService.recordImpression(adId, appId, clicked, userAgent, ipAddress);

  return NextResponse.json(
    {
      message: "Impression recorded successfully",
      clicked,
    },
    { status: 201 },
  );
}

// Apply error handling middleware
export const POST = withApiErrorHandling(recordImpressionHandler);
