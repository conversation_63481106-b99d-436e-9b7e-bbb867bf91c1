import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { z } from "zod/v4";

import { authOptions } from "@/lib/auth";
import { validateData } from "@/lib/validation";
import { AdService } from "@/lib/services/ad";
import { withApiErrorHandling } from "@/lib/error-handler";

// Advertisement creation validation
const advertisementSchema = z.object({
  name: z
    .string({ error: "Campaign name is required" })
    .max(100, "Campaign name too long"),
  description: z
    .string({ error: "Description is required" })
    .max(500, "Description too long"),
  productUrl: z.url({ error: "Invalid product URL" }),
  imageUrl: z.url({ error: "Invalid image URL" }).optional().nullable(),
  targetTopics: z.array(z.string()).max(10, "Too many target topics"),
  budget: z
    .number()
    .min(1, "Budget must be at least $1")
    .max(100000, "Budget too high"),
  bidType: z.enum(["CPC", "CPM"]),
  bidAmount: z
    .number()
    .min(0.01, "Bid amount must be at least $0.01")
    .max(100, "Bid amount too high"),
});

async function getAdsHandler(_request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session || !session.user.roles.includes("ADVERTISER")) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Use AdService to get user ads
  const ads = await AdService.getUserAds(session.user.id);

  return NextResponse.json({ ads });
}

// Apply error handling middleware
export const GET = withApiErrorHandling(getAdsHandler);

async function createAdHandler(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);

  if (!session || !session.user.roles.includes("ADVERTISER")) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const body = await request.json();

  // Validate input using Zod schema
  const validation = validateData(advertisementSchema, body);

  if (!validation.success) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  const {
    name,
    description,
    productUrl,
    imageUrl,
    targetTopics,
    budget,
    bidType,
    bidAmount,
  } = validation.data;

  // Use AdService to create ad
  const ad = await AdService.createAd({
    userId: session.user.id,
    name,
    description,
    productUrl,
    imageUrl: imageUrl || undefined,
    targetTopics,
    budget,
    bidType,
    bidAmount,
  });

  return NextResponse.json(
    {
      message: "Advertisement created successfully",
      ad,
    },
    { status: 201 },
  );
}

// Apply error handling middleware
export const POST = withApiErrorHandling(createAdHandler);
